import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  ScrollView,
  Text,
  TouchableOpacity,
  StatusBar,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import MyListService, { MY_LIST_CATEGORIES, CATEGORY_LABELS } from '../services/MyListService';
import MediaRow from '../components/MediaRow';

const MyListScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null); // null = all categories
  const [myListData, setMyListData] = useState([]);
  const [stats, setStats] = useState(null);
  const insets = useSafeAreaInsets();

  const categories = [
    { key: null, label: 'All' },
    ...Object.entries(MY_LIST_CATEGORIES).map(([key, value]) => ({
      key: value,
      label: CATEGORY_LABELS[value]
    }))
  ];

  useEffect(() => {
    loadMyListData();
  }, [selectedCategory]);

  useFocusEffect(
    useCallback(() => {
      loadMyListData();
    }, [selectedCategory])
  );

  const loadMyListData = async () => {
    try {
      setLoading(true);
      const [listData, statsData] = await Promise.all([
        selectedCategory 
          ? MyListService.getMyListByCategory(selectedCategory)
          : MyListService.getAllMyList(),
        MyListService.getMyListStats()
      ]);
      
      setMyListData(listData);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading my list:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadMyListData();
  };

  const handleItemPress = (item) => {
    navigation.navigate('MediaDetail', { 
      item: { ...item, media_type: item.mediaType }, 
      mediaType: item.mediaType 
    });
  };

  const handleRemoveItem = async (item) => {
    Alert.alert(
      'Remove from My List',
      `Remove "${item.title}" from your list?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await MyListService.removeFromMyList(item, item.mediaType);
              await loadMyListData();
            } catch (error) {
              console.error('Error removing item:', error);
            }
          },
        },
      ]
    );
  };

  const handleCategoryPress = (category) => {
    setSelectedCategory(category);
  };

  const renderCategoryTabs = () => (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false}
      style={slothStyles.categoryTabsContainer}
      contentContainerStyle={slothStyles.categoryTabsContent}
    >
      {categories.map((category) => (
        <TouchableOpacity
          key={category.key || 'all'}
          style={[
            slothStyles.categoryTab,
            selectedCategory === category.key && slothStyles.categoryTabActive
          ]}
          onPress={() => handleCategoryPress(category.key)}
        >
          <Text style={[
            slothStyles.categoryTabText,
            selectedCategory === category.key && slothStyles.categoryTabTextActive
          ]}>
            {category.label}
            {stats && category.key && stats.byCategory[category.key] > 0 && (
              ` (${stats.byCategory[category.key]})`
            )}
            {category.key === null && stats && (
              ` (${stats.total})`
            )}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const renderEmptyState = () => (
    <View style={slothStyles.emptyStateContainer}>
      <Ionicons name="bookmark-outline" size={64} color={SLOTH_COLORS.textSecondary} />
      <Text style={slothStyles.emptyStateTitle}>
        {selectedCategory 
          ? `No ${CATEGORY_LABELS[selectedCategory]} items`
          : 'Your list is empty'
        }
      </Text>
      <Text style={slothStyles.emptyStateSubtitle}>
        {selectedCategory
          ? `Add movies and TV shows to your ${CATEGORY_LABELS[selectedCategory]} list`
          : 'Add movies and TV shows to your list to see them here'
        }
      </Text>
      <TouchableOpacity 
        style={slothStyles.emptyStateButton}
        onPress={() => navigation.navigate('Home')}
      >
        <Text style={slothStyles.emptyStateButtonText}>Browse Content</Text>
      </TouchableOpacity>
    </View>
  );

  const renderStatsHeader = () => {
    if (!stats || selectedCategory) return null;

    return (
      <View style={slothStyles.statsContainer}>
        <Text style={slothStyles.statsTitle}>My List Overview</Text>
        <View style={slothStyles.statsRow}>
          <View style={slothStyles.statItem}>
            <Text style={slothStyles.statNumber}>{stats.byMediaType.movie}</Text>
            <Text style={slothStyles.statLabel}>Movies</Text>
          </View>
          <View style={slothStyles.statItem}>
            <Text style={slothStyles.statNumber}>{stats.byMediaType.tv}</Text>
            <Text style={slothStyles.statLabel}>TV Shows</Text>
          </View>
          <View style={slothStyles.statItem}>
            <Text style={slothStyles.statNumber}>{stats.total}</Text>
            <Text style={slothStyles.statLabel}>Total</Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={[slothStyles.container, slothStyles.loadingContainer]}>
        <StatusBar barStyle="light-content" />
        <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
      </View>
    );
  }

  return (
    <View style={slothStyles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Header */}
      <View style={[slothStyles.screenHeader, { paddingTop: insets.top + 10 }]}>
        <TouchableOpacity 
          style={slothStyles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
        </TouchableOpacity>
        <Text style={slothStyles.screenTitle}>My List</Text>
        <View style={slothStyles.headerSpacer} />
      </View>

      <ScrollView
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={SLOTH_COLORS.white}
          />
        }
      >
        {renderCategoryTabs()}
        {renderStatsHeader()}
        
        {myListData.length === 0 ? (
          renderEmptyState()
        ) : (
          <MediaRow
            title={selectedCategory 
              ? CATEGORY_LABELS[selectedCategory]
              : 'All Items'
            }
            data={myListData}
            onItemPress={handleItemPress}
            showRemoveButton={true}
            onRemove={handleRemoveItem}
          />
        )}
      </ScrollView>
    </View>
  );
};

export default MyListScreen;
