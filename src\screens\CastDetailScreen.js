import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  Image,
  Alert,
  useWindowDimensions,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import tmdbApi from '../services/tmdbApi';
import MediaRow from '../components/MediaRow';

const CastDetailScreen = ({ navigation, route }) => {
  const { castMember } = route.params;
  const [loading, setLoading] = useState(true);
  const [personDetails, setPersonDetails] = useState(null);
  const [filmography, setFilmography] = useState([]);
  const [filteredFilmography, setFilteredFilmography] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState('popular'); // 'popular', 'latest', 'movies', 'tv'
  const insets = useSafeAreaInsets();
  const { width } = useWindowDimensions();

  useEffect(() => {
    loadPersonDetails();
  }, []);

  useEffect(() => {
    applyFilmographyFilter();
  }, [filmography, selectedFilter]);

  const loadPersonDetails = async () => {
    try {
      setLoading(true);
      const details = await tmdbApi.getPersonDetails(castMember.id);
      setPersonDetails(details);

      // Combine movie and TV credits
      const movieCredits = (details.movie_credits?.cast || []).map(item => ({
        ...item,
        media_type: 'movie',
        release_date: item.release_date,
        title: item.title,
        unique_id: `movie-${item.id}`, // Add unique identifier
      }));

      const tvCredits = (details.tv_credits?.cast || []).map(item => ({
        ...item,
        media_type: 'tv',
        release_date: item.first_air_date,
        title: item.name,
        unique_id: `tv-${item.id}`, // Add unique identifier
      }));

      const allCredits = [...movieCredits, ...tvCredits]
        .filter(item => item.poster_path) // Only include items with posters
        .sort((a, b) => {
          // Sort by popularity by default
          return (b.popularity || 0) - (a.popularity || 0);
        });

      setFilmography(allCredits);
    } catch (error) {
      console.error('Error loading person details:', error);
      Alert.alert('Error', 'Failed to load actor details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const applyFilmographyFilter = () => {
    let filtered = [...filmography];

    switch (selectedFilter) {
      case 'popular':
        filtered.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
        break;
      case 'latest':
        filtered.sort((a, b) => {
          const dateA = new Date(a.release_date || '1900-01-01');
          const dateB = new Date(b.release_date || '1900-01-01');
          return dateB - dateA;
        });
        break;
      case 'movies':
        filtered = filtered.filter(item => item.media_type === 'movie');
        filtered.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
        break;
      case 'tv':
        filtered = filtered.filter(item => item.media_type === 'tv');
        filtered.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
        break;
    }

    setFilteredFilmography(filtered.slice(0, 20)); // Limit to 20 items
  };

  const handleItemPress = (item) => {
    const mediaType = item.media_type || (item.first_air_date ? 'tv' : 'movie');
    navigation.navigate('MediaDetail', { item, mediaType });
  };

  const renderFilterTabs = () => {
    const filters = [
      { key: 'popular', label: 'Most Popular' },
      { key: 'latest', label: 'Latest' },
      { key: 'movies', label: 'Movies Only' },
      { key: 'tv', label: 'TV Shows Only' },
    ];

    return (
      <View style={styles.filterTabsContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterTabsScroll}>
          {filters.map(filter => (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.filterTab,
                selectedFilter === filter.key && styles.filterTabActive
              ]}
              onPress={() => setSelectedFilter(filter.key)}
            >
              <Text style={[
                styles.filterTabText,
                selectedFilter === filter.key && styles.filterTabTextActive
              ]}>
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderPersonHeader = () => {
    if (!personDetails) return null;

    const imageUrl = personDetails.profile_path
      ? tmdbApi.getImageUrl(personDetails.profile_path, 'w500')
      : null;

    return (
      <View style={styles.headerContainer}>
        <View style={styles.profileSection}>
          {imageUrl ? (
            <Image source={{ uri: imageUrl }} style={styles.profileImage} />
          ) : (
            <View style={[styles.profileImage, styles.profileImagePlaceholder]}>
              <Ionicons name="person" size={60} color={SLOTH_COLORS.textSecondary} />
            </View>
          )}
          <View style={styles.profileInfo}>
            <Text style={styles.personName}>{personDetails.name}</Text>
            {personDetails.known_for_department && (
              <Text style={styles.personDepartment}>
                Known for {personDetails.known_for_department}
              </Text>
            )}
            {personDetails.birthday && (
              <Text style={styles.personDetail}>
                Born: {new Date(personDetails.birthday).toLocaleDateString()}
              </Text>
            )}
            {personDetails.place_of_birth && (
              <Text style={styles.personDetail}>
                From: {personDetails.place_of_birth}
              </Text>
            )}
          </View>
        </View>

        {personDetails.biography && (
          <View style={styles.biographySection}>
            <Text style={styles.biographyTitle}>Biography</Text>
            <Text style={styles.biographyText} numberOfLines={6}>
              {personDetails.biography}
            </Text>
          </View>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={[slothStyles.container, { paddingTop: insets.top }]}>
        <View style={slothStyles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
          </TouchableOpacity>
          <Text style={slothStyles.screenTitle}>Cast Details</Text>
          <View style={slothStyles.headerSpacer} />
        </View>
        <View style={slothStyles.loadingContainer}>
          <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
        </View>
      </View>
    );
  }

  return (
    <View style={[slothStyles.container, { paddingTop: insets.top }]}>
      {/* Header */}
      <View style={slothStyles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
        </TouchableOpacity>
        <Text style={slothStyles.screenTitle}>Cast Details</Text>
        <View style={slothStyles.headerSpacer} />
      </View>

      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {renderPersonHeader()}
        
        {/* Filmography Section */}
        {filteredFilmography.length > 0 && (
          <View style={styles.filmographySection}>
            <Text style={styles.filmographyTitle}>Filmography</Text>
            {renderFilterTabs()}
            <MediaRow
              title={`${filteredFilmography.length} ${selectedFilter === 'movies' ? 'Movies' : 
                selectedFilter === 'tv' ? 'TV Shows' : 'Credits'}`}
              data={filteredFilmography}
              onItemPress={handleItemPress}
            />
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = {
  headerContainer: {
    padding: 20,
  },
  profileSection: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  profileImage: {
    width: 120,
    height: 180,
    borderRadius: 12,
    marginRight: 20,
  },
  profileImagePlaceholder: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  personName: {
    color: SLOTH_COLORS.white,
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  personDepartment: {
    color: SLOTH_COLORS.primary,
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  personDetail: {
    color: SLOTH_COLORS.textSecondary,
    fontSize: 14,
    marginBottom: 4,
  },
  biographySection: {
    marginTop: 10,
  },
  biographyTitle: {
    color: SLOTH_COLORS.white,
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  biographyText: {
    color: SLOTH_COLORS.textSecondary,
    fontSize: 14,
    lineHeight: 20,
  },
  filmographySection: {
    marginTop: 10,
  },
  filmographyTitle: {
    color: SLOTH_COLORS.white,
    fontSize: 20,
    fontWeight: 'bold',
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  filterTabsContainer: {
    marginBottom: 15,
  },
  filterTabsScroll: {
    paddingHorizontal: 20,
  },
  filterTab: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginRight: 10,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  filterTabActive: {
    backgroundColor: SLOTH_COLORS.primary,
  },
  filterTabText: {
    color: SLOTH_COLORS.textSecondary,
    fontSize: 14,
    fontWeight: '600',
  },
  filterTabTextActive: {
    color: SLOTH_COLORS.white,
  },
};

export default CastDetailScreen;
