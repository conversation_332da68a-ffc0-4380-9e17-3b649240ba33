import React, { memo } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import tmdbApi from '../services/tmdbApi';

const CastCard = memo(({ castMember, onPress }) => {
  const imageUrl = castMember.profile_path
    ? tmdbApi.getImageUrl(castMember.profile_path, 'w185')
    : null;

  return (
    <TouchableOpacity
      style={slothStyles.castCardContainer}
      onPress={() => onPress && onPress(castMember)}
      activeOpacity={0.8}
    >
      {imageUrl ? (
        <Image
          source={{ uri: imageUrl }}
          style={slothStyles.castCardImage}
        />
      ) : (
        <View style={[slothStyles.castCardImage, { justifyContent: 'center', alignItems: 'center' }]}>
          <Ionicons name="person" size={40} color={SLOTH_COLORS.textSecondary} />
        </View>
      )}
      <Text style={slothStyles.castCardName} numberOfLines={2}>
        {castMember.name}
      </Text>
      <Text style={slothStyles.castCardCharacter} numberOfLines={2}>
        {castMember.character}
      </Text>
    </TouchableOpacity>
  );
});

export default CastCard;
