import React, { memo, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth'; // Use new styles
import MediaCard from './MediaCard';

const MediaRow = memo(({
  title,
  data,
  onItemPress,
  loading = false,
  showRemoveButton = false,
  onRemove = null,
}) => {
  const renderItem = useCallback(({ item }) => {
    return (
      <MediaCard
        item={item}
        onPress={onItemPress}
        showRemoveButton={showRemoveButton}
        onRemove={onRemove}
      />
    );
  }, [onItemPress, showRemoveButton, onRemove]);
  
  const keyExtractor = useCallback((item) => item.unique_id || item.id.toString(), []);

  // Card dimensions for getItemLayout optimization
  const CARD_WIDTH = 140;
  const CARD_MARGIN = 10;
  const CARD_FULL_WIDTH = CARD_WIDTH + CARD_MARGIN;

  if (loading) {
    return (
      <View style={slothStyles.mediaRowContainer}>
        <Text style={slothStyles.mediaRowTitle}>{title}</Text>
        <View style={{ height: 210, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
        </View>
      </View>
    );
  }

  if (!data || data.length === 0) {
    return null; // Don't render empty rows
  }

  return (
    <View style={slothStyles.mediaRowContainer}>
      <Text style={slothStyles.mediaRowTitle}>{title}</Text>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        horizontal
        showsHorizontalScrollIndicator={false}
        nestedScrollEnabled={true}
        scrollEnabled={true}
        contentContainerStyle={slothStyles.mediaRowScroll}
        removeClippedSubviews={false}
        maxToRenderPerBatch={7}
        windowSize={5}
        initialNumToRender={5}
        // Gesture handling to prevent conflicts with navigation gestures
        onScrollBeginDrag={() => {}}
        onScrollEndDrag={() => {}}
        scrollEventThrottle={16}
        // This helps prioritize horizontal scrolling over navigation gestures
        gestureResponseDistance={10}
        getItemLayout={(_, index) => ({
          length: CARD_FULL_WIDTH,
          offset: CARD_FULL_WIDTH * index,
          index,
        })}
      />
    </View>
  );
});

export default MediaRow;