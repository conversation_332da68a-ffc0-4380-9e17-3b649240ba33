import { SORA_BASE_URL, PROXY_BASE_URL } from '../utils/constants';

class SoraApi {
  constructor() {
    this.baseUrl = SORA_BASE_URL;
    this.proxyUrl = PROXY_BASE_URL;
  }

  async makeRequest(endpoint, params = {}) {
    try {
      const url = new URL(`${this.baseUrl}${endpoint}`);
      
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          url.searchParams.append(key, params[key]);
        }
      });

      const response = await fetch(url.toString());
      
      if (!response.ok) {
        throw new Error(`SoraStream API Error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('SoraStream API Request failed:', error);
      throw error;
    }
  }

  // Search media
  async searchMedia(query) {
    return this.makeRequest('/search', { query });
  }

  // Get media details
  async getMediaDetails(mediaType, tmdbId) {
    return this.makeRequest(`/media/${mediaType}/${tmdbId}`);
  }

  // Get trending content
  async getTrending(mediaType, timeWindow = 'day') {
    return this.makeRequest(`/trending/${mediaType}`, { time_window: timeWindow });
  }

  // Get providers for media type
  async getProviders(mediaType, region = 'US') {
    return this.makeRequest(`/providers/${mediaType}`, { region });
  }

  // Discover content by provider
  async discoverByProvider(mediaType, providers, region = 'US') {
    return this.makeRequest(`/discover/provider/${mediaType}`, { 
      providers, 
      region 
    });
  }

  // Get movie streams
  async getMovieStreams(tmdbId) {
    return this.makeRequest(`/streams/movie/${tmdbId}`);
  }

  // Get TV show streams
  async getTVStreams(tmdbId, season, episode) {
    return this.makeRequest(`/streams/tv/${tmdbId}/${season}/${episode}`);
  }

  // Get subtitles
  async getSubtitles(fileId) {
    return this.makeRequest(`/subtitles/${fileId}.vtt`);
  }

  // Helper method to get proxied stream URL
  getProxiedStreamUrl(originalUrl) {
    if (!originalUrl) return null;
    return `${this.proxyUrl}${encodeURIComponent(originalUrl)}`;
  }

  // Process stream response to get playable URLs
  processStreamResponse(streamData) {
    if (!streamData || !streamData.streams) {
      return null;
    }

    const processedStreams = streamData.streams.map(stream => ({
      ...stream,
      // Don't proxy the URL - use it directly as it's already processed
      url: stream.url,
      quality: stream.quality || 'auto',
      name: stream.name || 'Unknown Quality',
      is_m3u8: stream.is_m3u8 || false,
      is_dash: stream.is_dash || false,
      headers: stream.headers || null,
      referer: stream.referer || null
    }));

    return {
      streams: processedStreams,
      subtitles: streamData.subtitles ? streamData.subtitles.map(sub => ({
        ...sub,
        url: sub.url || null
      })) : []
    };
  }

  // Get best quality stream
  getBestQualityStream(streamData) {
    if (!streamData || !streamData.streams || streamData.streams.length === 0) {
      return null;
    }

    // Sort by quality (higher number = better quality)
    const sortedStreams = streamData.streams.sort((a, b) => {
      const qualityA = typeof a.quality === 'number' ? a.quality : 0;
      const qualityB = typeof b.quality === 'number' ? b.quality : 0;
      return qualityB - qualityA;
    });

    // Return highest quality stream
    return sortedStreams[0];
  }
}

export default new SoraApi();
