import React, { useState, useEffect } from 'react';
import {
  View,
  StatusBar,
  Text,
  TouchableOpacity,
  FlatList,
  ImageBackground,
  ActivityIndicator,
  Alert,
  Modal,
  UIManager,
  LayoutAnimation,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import tmdbApi from '../services/tmdbApi';
import WatchHistoryService from '../services/WatchHistoryService';

// Enable LayoutAnimation for Android for the "Read More" feature
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

// Enhanced Episode Card Component with watch progress
const EpisodeCard = ({ episode, show, season, onPress }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [watchProgress, setWatchProgress] = useState(null);

  useEffect(() => {
    const loadWatchProgress = async () => {
      try {
        const progress = await WatchHistoryService.getProgress(show, 'tv', season, episode);
        setWatchProgress(progress);
      } catch (error) {
        console.error('Error loading episode watch progress:', error);
      }
    };

    loadWatchProgress();
  }, [show.id, season.season_number, episode.episode_number]);

  const imageUrl = episode.still_path
    ? tmdbApi.getImageUrl(episode.still_path, 'w780')
    : tmdbApi.getBackdropUrl(show.backdrop_path);

  const airDate = episode.air_date
    ? new Date(episode.air_date).toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' })
    : 'Unreleased';

  const metaText = [
    episode.runtime ? `${episode.runtime}m` : null,
    airDate
  ].filter(Boolean).join('  •  ');

  const toggleExpand = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setIsExpanded(!isExpanded);
  };

  const isWatched = watchProgress && watchProgress.progressPercent >= 0.9;
  const isInProgress = watchProgress && watchProgress.progressPercent > 0.05 && watchProgress.progressPercent < 0.9;

  return (
    <View style={slothStyles.episodeCardContainer}>
      <TouchableOpacity activeOpacity={0.8} onPress={onPress}>
        <ImageBackground source={{ uri: imageUrl }} style={slothStyles.episodeImageContainer}>
          <View style={slothStyles.episodePlayIconOverlay}>
              <Ionicons name="play" size={28} color={SLOTH_COLORS.white} style={{ marginLeft: 3 }}/>
          </View>

          {/* Resume overlay for in-progress episodes */}
          {isInProgress && (
            <View style={slothStyles.episodeResumeOverlay}>
              <Text style={slothStyles.episodeResumeText}>Resume</Text>
            </View>
          )}

          {/* Watched indicator for completed episodes */}
          {isWatched && (
            <View style={slothStyles.episodeWatchedOverlay}>
              <Ionicons name="checkmark" size={16} color={SLOTH_COLORS.white} />
            </View>
          )}

          {/* Progress bar for episodes with progress */}
          {watchProgress && watchProgress.progressPercent > 0 && (
            <View style={slothStyles.episodeProgressContainer}>
              <View style={slothStyles.episodeProgressBar}>
                <View
                  style={[
                    slothStyles.episodeProgressFill,
                    { width: `${Math.min(watchProgress.progressPercent * 100, 100)}%` }
                  ]}
                />
              </View>
            </View>
          )}
        </ImageBackground>
      </TouchableOpacity>

      <View style={slothStyles.episodeInfoBlock}>
        <View style={slothStyles.episodeTitleRow}>
          <Text style={slothStyles.episodeNumber}>{episode.episode_number}.</Text>
          <Text style={slothStyles.episodeTitle} numberOfLines={1}>{episode.name}</Text>
        </View>

        <Text style={slothStyles.episodeMetaText}>{metaText}</Text>

        {episode.overview && (
          <View>
            <Text style={slothStyles.episodeOverview} numberOfLines={isExpanded ? undefined : 2}>{episode.overview}</Text>
            {/* Only show "Read More" if the overview is long enough to be truncated */}
            {episode.overview.length > 100 && (
                <TouchableOpacity onPress={toggleExpand}>
                    <Text style={slothStyles.episodeReadMoreText}>{isExpanded ? 'Read Less' : 'Read More'}</Text>
                </TouchableOpacity>
            )}
          </View>
        )}
      </View>
    </View>
  );
};


const TVEpisodesScreen = ({ route, navigation }) => {
  const { item } = route.params;
  const [loading, setLoading] = useState(true);
  const [seasons, setSeasons] = useState([]);
  const [selectedSeason, setSelectedSeason] = useState(null);
  const [episodes, setEpisodes] = useState([]);
  const [loadingEpisodes, setLoadingEpisodes] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const insets = useSafeAreaInsets();

  useEffect(() => { loadTVDetails(); }, []);

  useEffect(() => {
    if (selectedSeason?.season_number !== undefined) {
      loadSeasonEpisodes(selectedSeason.season_number);
    }
  }, [selectedSeason]);

  const loadTVDetails = async () => {
    try {
      setLoading(true);
      const tvDetails = await tmdbApi.getTVDetails(item.id);
      const availableSeasons = (tvDetails.seasons || []).filter(s => s.season_number > 0 && s.episode_count > 0);
      setSeasons(availableSeasons);
      
      if (availableSeasons.length > 0) {
        setSelectedSeason(availableSeasons[0]);
      }
    } catch (error) {
      console.error("Error loading TV Details:", error);
      Alert.alert('Error', 'Failed to load TV show details.');
    } finally {
      setLoading(false);
    }
  };

  const loadSeasonEpisodes = async (seasonNumber) => {
    try {
      setLoadingEpisodes(true);
      const seasonDetails = await tmdbApi.getTVSeasonDetails(item.id, seasonNumber);
      setEpisodes(seasonDetails.episodes || []);
    } catch (error) {
      console.error("Error loading TV Season Details:", error);
      setEpisodes([]);
    } finally {
      setLoadingEpisodes(false);
    }
  };

  const handleSeasonSelect = (season) => {
    setSelectedSeason(season);
    setIsModalVisible(false);
  };
  
  const handleEpisodePress = (episode) => {
    navigation.navigate('Player', {
      item: { ...item, media_type: 'tv' },
      mediaType: 'tv',
      season: selectedSeason,
      episode: episode
    });
  };

  if (loading) {
    return (
      <View style={slothStyles.loadingContainer}>
        <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
      </View>
    );
  }

  return (
    <ImageBackground 
        source={{uri: tmdbApi.getPosterUrl(item.poster_path)}}
        style={slothStyles.episodesScreenContainer}
        blurRadius={30} // The magic for the blurred background
    >
      {/* Dark overlay to ensure text readability */}
      <View style={slothStyles.episodesScreenOverlay} />
      
      <View style={[slothStyles.container, {backgroundColor: 'transparent', paddingTop: insets.top}]}>
        <StatusBar barStyle="light-content" />
        
        <View style={slothStyles.episodesHeader}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={slothStyles.iconContainer}>
            <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
          </TouchableOpacity>
          <Text style={slothStyles.episodesHeaderTitle} numberOfLines={1}>{item.name}</Text>
        </View>

        {selectedSeason && (
          <TouchableOpacity style={slothStyles.seasonSelector} onPress={() => setIsModalVisible(true)}>
            <Text style={slothStyles.seasonSelectorText}>{selectedSeason.name}</Text>
            <Ionicons name="chevron-down" size={20} color={SLOTH_COLORS.white} />
          </TouchableOpacity>
        )}

        {loadingEpisodes ? (
          <View style={slothStyles.loadingContainer}><ActivityIndicator size="large" color={SLOTH_COLORS.primary} /></View>
        ) : (
          <FlatList
            data={episodes}
            renderItem={({ item: episode }) => (
              <EpisodeCard
                episode={episode}
                show={item}
                season={selectedSeason}
                onPress={() => handleEpisodePress(episode)}
              />
            )}
            keyExtractor={(ep) => `episode-${ep.id}`}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: insets.bottom + 20 }}
          />
        )}

        <Modal animationType="fade" transparent={true} visible={isModalVisible} onRequestClose={() => setIsModalVisible(false)}>
          <TouchableOpacity style={slothStyles.modalOverlay} activeOpacity={1} onPressOut={() => setIsModalVisible(false)}>
              <View style={[slothStyles.modalContent, {marginBottom: insets.bottom}]}>
                  <Text style={slothStyles.modalHeader}>Select Season</Text>
                  <FlatList 
                      data={seasons}
                      keyExtractor={(s) => s.id.toString()}
                      renderItem={({item: season}) => (
                          <TouchableOpacity style={slothStyles.modalItem} onPress={() => handleSeasonSelect(season)}>
                              <Text style={slothStyles.modalItemText}>{season.name}</Text>
                          </TouchableOpacity>
                      )}
                  />
              </View>
          </TouchableOpacity>
        </Modal>
      </View>
    </ImageBackground>
  );
};

export default TVEpisodesScreen;