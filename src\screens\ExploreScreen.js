import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import tmdbApi from '../services/tmdbApi';
import MediaCard from '../components/MediaCard';

const ExploreScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState([]);
  const [genres, setGenres] = useState([]);
  const [selectedGenre, setSelectedGenre] = useState(null);
  const [selectedYear, setSelectedYear] = useState(null);
  const [selectedRating, setSelectedRating] = useState(null);
  const [selectedMediaType, setSelectedMediaType] = useState('all'); // 'all', 'movie', 'tv'
  const [selectedSort, setSelectedSort] = useState('popularity.desc');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const insets = useSafeAreaInsets();

  useEffect(() => {
    loadGenres();
    loadInitialResults();
  }, []);

  useEffect(() => {
    if (genres.length > 0) {
      applyFilters();
    }
  }, [selectedGenre, selectedYear, selectedRating, selectedMediaType, selectedSort]);

  const loadGenres = async () => {
    try {
      const [movieGenres, tvGenres] = await Promise.all([
        tmdbApi.getMovieGenres(),
        tmdbApi.getTVGenres()
      ]);

      // Combine and deduplicate genres
      const allGenres = [...movieGenres.genres, ...tvGenres.genres];
      const uniqueGenres = Array.from(
        new Map(allGenres.map(g => [g.id, g])).values()
      ).sort((a, b) => a.name.localeCompare(b.name));

      // Add unique keys to prevent duplicate key warnings
      const genresWithUniqueKeys = uniqueGenres.map(genre => ({
        ...genre,
        unique_key: `genre-${genre.id}`
      }));

      setGenres(genresWithUniqueKeys);
    } catch (error) {
      console.error('Error loading genres:', error);
    }
  };

  const loadInitialResults = async () => {
    try {
      setLoading(true);
      const trendingData = await tmdbApi.getTrending('all');
      setResults(trendingData.results || []);
    } catch (error) {
      console.error('Error loading initial results:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = async (resetPage = true) => {
    try {
      setLoading(true);
      const currentPage = resetPage ? 1 : page;
      
      const filters = {};
      
      if (selectedGenre) {
        filters.with_genres = selectedGenre.id;
      }
      
      if (selectedYear) {
        if (selectedMediaType === 'movie') {
          filters.primary_release_year = selectedYear;
        } else if (selectedMediaType === 'tv') {
          filters.first_air_date_year = selectedYear;
        }
      }
      
      if (selectedRating) {
        filters['vote_average.gte'] = selectedRating.min;
        filters['vote_average.lte'] = selectedRating.max;
      }
      
      filters.sort_by = selectedSort;

      let newResults = [];
      
      if (selectedMediaType === 'all') {
        // Get both movies and TV shows
        const [movieData, tvData] = await Promise.all([
          tmdbApi.discoverMovies(filters, currentPage),
          tmdbApi.discoverTV(filters, currentPage)
        ]);
        
        // Combine and shuffle results
        const combined = [
          ...(movieData.results || []).map(item => ({ ...item, media_type: 'movie' })),
          ...(tvData.results || []).map(item => ({ ...item, media_type: 'tv' }))
        ];
        
        // Sort by popularity if that's the selected sort
        if (selectedSort === 'popularity.desc') {
          combined.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
        }
        
        newResults = combined.slice(0, 20); // Limit to 20 items per page
      } else if (selectedMediaType === 'movie') {
        const movieData = await tmdbApi.discoverMovies(filters, currentPage);
        newResults = (movieData.results || []).map(item => ({ ...item, media_type: 'movie' }));
      } else if (selectedMediaType === 'tv') {
        const tvData = await tmdbApi.discoverTV(filters, currentPage);
        newResults = (tvData.results || []).map(item => ({ ...item, media_type: 'tv' }));
      }

      if (resetPage) {
        setResults(newResults);
        setPage(1);
      } else {
        setResults(prev => [...prev, ...newResults]);
      }
      
      setHasMore(newResults.length === 20);
      
    } catch (error) {
      console.error('Error applying filters:', error);
      Alert.alert('Error', 'Failed to load filtered results. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleItemPress = (item) => {
    const mediaType = item.media_type || (item.first_air_date ? 'tv' : 'movie');
    navigation.navigate('MediaDetail', { item, mediaType });
  };

  const renderItem = ({ item }) => (
    <View style={slothStyles.searchGridItem}>
      <MediaCard item={item} onPress={handleItemPress} gridMode={true} />
    </View>
  );

  const renderGenreFilter = () => (
    <View style={styles.filterSection}>
      <Text style={styles.filterTitle}>Genre</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
        <TouchableOpacity
          style={[styles.filterChip, !selectedGenre && styles.filterChipActive]}
          onPress={() => setSelectedGenre(null)}
        >
          <Text style={[styles.filterChipText, !selectedGenre && styles.filterChipTextActive]}>
            All Genres
          </Text>
        </TouchableOpacity>
        {genres.map(genre => (
          <TouchableOpacity
            key={genre.unique_key || genre.id}
            style={[styles.filterChip, selectedGenre?.id === genre.id && styles.filterChipActive]}
            onPress={() => setSelectedGenre(genre)}
          >
            <Text style={[
              styles.filterChipText,
              selectedGenre?.id === genre.id && styles.filterChipTextActive
            ]}>
              {genre.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderYearFilter = () => {
    const currentYear = tmdbApi.getCurrentYear();
    const years = Array.from({ length: 30 }, (_, i) => currentYear - i);
    
    return (
      <View style={styles.filterSection}>
        <Text style={styles.filterTitle}>Release Year</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
          <TouchableOpacity
            style={[styles.filterChip, !selectedYear && styles.filterChipActive]}
            onPress={() => setSelectedYear(null)}
          >
            <Text style={[styles.filterChipText, !selectedYear && styles.filterChipTextActive]}>
              Any Year
            </Text>
          </TouchableOpacity>
          {years.map(year => (
            <TouchableOpacity
              key={year}
              style={[styles.filterChip, selectedYear === year && styles.filterChipActive]}
              onPress={() => setSelectedYear(year)}
            >
              <Text style={[
                styles.filterChipText,
                selectedYear === year && styles.filterChipTextActive
              ]}>
                {year}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderRatingFilter = () => {
    const ratings = tmdbApi.getRatingRanges();
    
    return (
      <View style={styles.filterSection}>
        <Text style={styles.filterTitle}>Rating</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
          {ratings.map((rating, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.filterChip, selectedRating === rating && styles.filterChipActive]}
              onPress={() => setSelectedRating(selectedRating === rating ? null : rating)}
            >
              <Text style={[
                styles.filterChipText,
                selectedRating === rating && styles.filterChipTextActive
              ]}>
                {rating.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderMediaTypeFilter = () => (
    <View style={styles.filterSection}>
      <Text style={styles.filterTitle}>Media Type</Text>
      <View style={styles.mediaTypeContainer}>
        {[
          { key: 'all', label: 'All' },
          { key: 'movie', label: 'Movies' },
          { key: 'tv', label: 'TV Shows' }
        ].map(type => (
          <TouchableOpacity
            key={type.key}
            style={[styles.filterChip, selectedMediaType === type.key && styles.filterChipActive]}
            onPress={() => setSelectedMediaType(type.key)}
          >
            <Text style={[
              styles.filterChipText,
              selectedMediaType === type.key && styles.filterChipTextActive
            ]}>
              {type.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <View style={[slothStyles.container, { paddingTop: insets.top }]}>
      {/* Header */}
      <View style={slothStyles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
        </TouchableOpacity>
        <Text style={slothStyles.screenTitle}>Explore</Text>
        <View style={slothStyles.headerSpacer} />
      </View>

      {/* Filters */}
      <ScrollView style={styles.filtersContainer} showsVerticalScrollIndicator={false}>
        {renderGenreFilter()}
        {renderYearFilter()}
        {renderRatingFilter()}
        {renderMediaTypeFilter()}
      </ScrollView>

      {/* Results */}
      {loading && results.length === 0 ? (
        <View style={slothStyles.loadingContainer}>
          <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
        </View>
      ) : (
        <FlatList
          data={results}
          keyExtractor={(item) => `${item.id}-${item.media_type || 'unknown'}`}
          renderItem={renderItem}
          numColumns={3}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingHorizontal: 5, paddingBottom: 20 }}
          ListHeaderComponent={() => (
            <Text style={styles.resultsTitle}>
              {results.length} Results
            </Text>
          )}
        />
      )}
    </View>
  );
};

const styles = {
  filtersContainer: {
    maxHeight: 200,
    backgroundColor: 'rgba(255, 255, 255, 0.02)',
    marginHorizontal: 15,
    marginBottom: 15,
    borderRadius: 12,
    padding: 15,
  },
  filterSection: {
    marginBottom: 15,
  },
  filterTitle: {
    color: SLOTH_COLORS.white,
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  filterScroll: {
    flexDirection: 'row',
  },
  filterChip: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  filterChipActive: {
    backgroundColor: SLOTH_COLORS.primary,
  },
  filterChipText: {
    color: SLOTH_COLORS.textSecondary,
    fontSize: 14,
    fontWeight: '600',
  },
  filterChipTextActive: {
    color: SLOTH_COLORS.white,
  },
  mediaTypeContainer: {
    flexDirection: 'row',
  },
  resultsTitle: {
    color: SLOTH_COLORS.white,
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
};

export default ExploreScreen;
