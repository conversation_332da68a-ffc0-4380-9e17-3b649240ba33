import React, { memo, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import VideoCard from './VideoCard';

const VideoRow = memo(({
  title = "Trailers & Videos",
  data,
  loading = false,
}) => {
  const renderItem = useCallback(({ item }) => {
    return <VideoCard video={item} />;
  }, []);
  
  const keyExtractor = useCallback((item) => item.id || item.key, []);

  // Card dimensions for getItemLayout optimization
  const CARD_WIDTH = 200;
  const CARD_MARGIN = 15;
  const CARD_FULL_WIDTH = CARD_WIDTH + CARD_MARGIN;

  if (loading) {
    return (
      <View style={slothStyles.videoRowContainer}>
        <Text style={slothStyles.videoRowTitle}>{title}</Text>
        <View style={{ height: 140, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
        </View>
      </View>
    );
  }

  if (!data || data.length === 0) {
    return null;
  }

  return (
    <View style={slothStyles.videoRowContainer}>
      <Text style={slothStyles.videoRowTitle}>{title}</Text>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        horizontal
        showsHorizontalScrollIndicator={false}
        nestedScrollEnabled={true}
        scrollEnabled={true}
        contentContainerStyle={slothStyles.videoRowScroll}
        removeClippedSubviews={false}
        maxToRenderPerBatch={5}
        windowSize={3}
        initialNumToRender={3}
        scrollEventThrottle={16}
        getItemLayout={(_, index) => ({
          length: CARD_FULL_WIDTH,
          offset: CARD_FULL_WIDTH * index,
          index,
        })}
      />
    </View>
  );
});

export default VideoRow;
