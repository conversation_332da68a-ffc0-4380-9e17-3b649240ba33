import { TMDB_API_KEY, TMDB_BASE_URL, TMDB_IMAGE_BASE_URL } from '../utils/constants';

class TMDBApi {
  constructor() {
    this.apiKey = TMDB_API_KEY;
    this.baseUrl = TMDB_BASE_URL;
    this.imageBaseUrl = TMDB_IMAGE_BASE_URL;
  }

  async makeRequest(endpoint, params = {}) {
    try {
      const url = new URL(`${this.baseUrl}${endpoint}`);
      url.searchParams.append('api_key', this.apiKey);
      
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          url.searchParams.append(key, params[key]);
        }
      });

      const response = await fetch(url.toString());
      
      if (!response.ok) {
        throw new Error(`TMDB API Error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('TMDB API Request failed:', error);
      throw error;
    }
  }

  // Get trending movies/TV shows
  async getTrending(mediaType = 'all', timeWindow = 'day') {
    return this.makeRequest(`/trending/${mediaType}/${timeWindow}`);
  }

  // Search for movies/TV shows
  async search(query, mediaType = 'multi') {
    if (mediaType === 'multi') {
      return this.makeRequest('/search/multi', { query });
    }
    return this.makeRequest(`/search/${mediaType}`, { query });
  }

  // Get movie details
  async getMovieDetails(movieId) {
    return this.makeRequest(`/movie/${movieId}`, {
      append_to_response: 'videos,credits,similar,recommendations'
    });
  }

  // Get TV show details
  async getTVDetails(tvId) {
    return this.makeRequest(`/tv/${tvId}`, {
      append_to_response: 'videos,credits,similar,recommendations'
    });
  }

  // Get popular movies
  async getPopularMovies(page = 1) {
    return this.makeRequest('/movie/popular', { page });
  }

  // Get popular TV shows
  async getPopularTV(page = 1) {
    return this.makeRequest('/tv/popular', { page });
  }

  // Get top rated movies
  async getTopRatedMovies(page = 1) {
    return this.makeRequest('/movie/top_rated', { page });
  }

  // Get top rated TV shows
  async getTopRatedTV(page = 1) {
    return this.makeRequest('/tv/top_rated', { page });
  }

  // Get now playing movies
  async getNowPlayingMovies(page = 1) {
    return this.makeRequest('/movie/now_playing', { page });
  }

  // Get upcoming movies
  async getUpcomingMovies(page = 1) {
    return this.makeRequest('/movie/upcoming', { page });
  }

  // Get TV show season details
  async getTVSeasonDetails(tvId, seasonNumber) {
    return this.makeRequest(`/tv/${tvId}/season/${seasonNumber}`);
  }

  // Helper method to get full image URL
  getImageUrl(path, size = 'w500') {
    if (!path) return null;
    return `${this.imageBaseUrl}/${size}${path}`;
  }

  // Helper method to get backdrop URL
  getBackdropUrl(path, size = 'w1280') {
    if (!path) return null;
    return `${this.imageBaseUrl}/${size}${path}`;
  }

  // Helper method to get poster URL
  getPosterUrl(path, size = 'w500') {
    if (!path) return null;
    return `${this.imageBaseUrl}/${size}${path}`;
  }

  // Get movies by genre
  async getMoviesByGenre(genreId, page = 1) {
    return this.makeRequest('/discover/movie', {
      with_genres: genreId,
      page,
      sort_by: 'popularity.desc'
    });
  }

  // Get TV shows by genre
  async getTVByGenre(genreId, page = 1) {
    return this.makeRequest('/discover/tv', {
      with_genres: genreId,
      page,
      sort_by: 'popularity.desc'
    });
  }
  
  // New method to discover media by network
  async discoverMediaByNetwork(mediaType = 'tv', networkId, page = 1) {
    return this.makeRequest(`/discover/${mediaType}`, {
      with_networks: networkId,
      page,
      sort_by: 'popularity.desc'
    });
  }


  // Get genres list
  async getMovieGenres() {
    return this.makeRequest('/genre/movie/list');
  }

  async getTVGenres() {
    return this.makeRequest('/genre/tv/list');
  }

  // Person/Cast related methods
  async getPersonDetails(personId) {
    return this.makeRequest(`/person/${personId}`, {
      append_to_response: 'movie_credits,tv_credits'
    });
  }

  async getPersonMovieCredits(personId) {
    return this.makeRequest(`/person/${personId}/movie_credits`);
  }

  async getPersonTVCredits(personId) {
    return this.makeRequest(`/person/${personId}/tv_credits`);
  }

  // Enhanced discover methods with filtering
  async discoverMovies(filters = {}, page = 1) {
    const params = {
      page,
      sort_by: 'popularity.desc',
      ...filters
    };
    return this.makeRequest('/discover/movie', params);
  }

  async discoverTV(filters = {}, page = 1) {
    const params = {
      page,
      sort_by: 'popularity.desc',
      ...filters
    };
    return this.makeRequest('/discover/tv', params);
  }

  // Get available years for filtering (helper method)
  getCurrentYear() {
    return new Date().getFullYear();
  }

  // Get rating ranges for filtering
  getRatingRanges() {
    return [
      { label: 'Any Rating', min: 0, max: 10 },
      { label: '9+ Excellent', min: 9, max: 10 },
      { label: '8+ Very Good', min: 8, max: 10 },
      { label: '7+ Good', min: 7, max: 10 },
      { label: '6+ Decent', min: 6, max: 10 },
      { label: '5+ Average', min: 5, max: 10 }
    ];
  }

  // Get sort options for filtering
  getSortOptions() {
    return [
      { label: 'Most Popular', value: 'popularity.desc' },
      { label: 'Latest Release', value: 'release_date.desc' },
      { label: 'Highest Rated', value: 'vote_average.desc' },
      { label: 'Title A-Z', value: 'title.asc' }
    ];
  }
}

export default new TMDBApi();