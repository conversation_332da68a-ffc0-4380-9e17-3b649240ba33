import React, { useState, useEffect } from 'react';
import {
  View,
  FlatList,
  ActivityIndicator,
  Text,
  Keyboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import tmdbApi from '../services/tmdbApi';
import AnalyticsService from '../services/AnalyticsService';
import SearchBar from '../components/SearchBar';
import MediaCard from '../components/MediaCard';

const SearchScreen = ({ navigation }) => {
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [topSearches, setTopSearches] = useState([]);
  const insets = useSafeAreaInsets();

  useEffect(() => {
    loadTopSearches();
  }, []);

  const loadTopSearches = async () => {
    try {
      setLoading(true);
      const moviesData = await tmdbApi.getTrending('movie');
      const filtered = (moviesData.results || []).filter(item => item.poster_path);
      setTopSearches(filtered);
    } catch (error) {
      console.error('Error loading top searches:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (query) => {
    setSearchQuery(query);
    if (!query) {
      setSearchResults([]);
      return;
    }
    try {
      setLoading(true);
      const results = await tmdbApi.search(query);
      const filteredResults = results.results.filter(item => item.poster_path) || [];
      setSearchResults(filteredResults);

      // Track search analytics
      await AnalyticsService.trackSearchQuery(query, filteredResults.length);
    } catch (error) {
      console.error('Error searching:', error);
      setSearchResults([]);

      // Track failed search
      await AnalyticsService.trackSearchQuery(query, 0);
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setSearchQuery('');
    setSearchResults([]);
  };

  const handleItemPress = (item) => {
    const mediaType = item.media_type || (item.first_air_date ? 'tv' : 'movie');
    navigation.navigate('MediaDetail', { item, mediaType });
  };

  const renderItem = ({ item }) => (
    <View style={slothStyles.searchGridItem}>
      <MediaCard item={item} onPress={handleItemPress} gridMode={true} />
    </View>
  );

  const renderEmptyComponent = () => {
    if (loading || !searchQuery) return null;
    return (
      <View style={slothStyles.emptyContainer}>
        <Ionicons name="film-outline" size={80} color={SLOTH_COLORS.textSecondary} />
        <Text style={slothStyles.emptyText}>No results for "{searchQuery}"</Text>
        <Text style={slothStyles.emptySubtext}>Check your spelling or try another search.</Text>
      </View>
    );
  };
  
  const dataToShow = searchQuery ? searchResults : topSearches;
  const title = searchQuery ? 'Results' : "Top Searches";

  return (
    // We apply top padding to the whole screen container
    <View style={[slothStyles.container, { paddingTop: insets.top }]}>
      {/* The new SearchBar acts as the entire header */}
      <SearchBar
        onBackPress={() => navigation.goBack()}
        onSearch={handleSearch}
        onClear={handleClear}
        autoFocus={true}
      />
      
      {loading && dataToShow.length === 0 ? (
        <View style={slothStyles.loadingContainer}>
          <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
        </View>
      ) : (
        <FlatList
            data={dataToShow}
            keyExtractor={(item) => item.id.toString()}
            renderItem={renderItem}
            numColumns={3}
            showsVerticalScrollIndicator={false}
            ListHeaderComponent={() => <Text style={slothStyles.searchPageTitle}>{title}</Text>}
            ListEmptyComponent={renderEmptyComponent}
            contentContainerStyle={{ paddingHorizontal: 5 }}
            onScrollBeginDrag={Keyboard.dismiss}
          />
      )}
    </View>
  );
};

export default SearchScreen;