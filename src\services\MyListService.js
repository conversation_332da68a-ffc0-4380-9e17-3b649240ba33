import AsyncStorage from '@react-native-async-storage/async-storage';

const MY_LIST_KEY = '@my_list';

// My List Categories
export const MY_LIST_CATEGORIES = {
  WANT_TO_WATCH: 'want_to_watch',
  CURRENTLY_WATCHING: 'currently_watching',
  COMPLETED: 'completed',
  FAVORITES: 'favorites',
  DROPPED: 'dropped'
};

export const CATEGORY_LABELS = {
  [MY_LIST_CATEGORIES.WANT_TO_WATCH]: 'Want to Watch',
  [MY_LIST_CATEGORIES.CURRENTLY_WATCHING]: 'Currently Watching',
  [MY_LIST_CATEGORIES.COMPLETED]: 'Completed',
  [MY_LIST_CATEGORIES.FAVORITES]: 'Favorites',
  [MY_LIST_CATEGORIES.DROPPED]: 'Dropped'
};

class MyListService {
  constructor() {
    this.myList = new Map();
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;
    
    try {
      const stored = await AsyncStorage.getItem(MY_LIST_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.myList = new Map(Object.entries(parsed));
      }
      this.initialized = true;
    } catch (error) {
      console.error('Error initializing my list:', error);
      this.myList = new Map();
      this.initialized = true;
    }
  }

  generateListKey(item, mediaType) {
    return `${mediaType}_${item.id}`;
  }

  async addToMyList(item, mediaType, category = MY_LIST_CATEGORIES.WANT_TO_WATCH) {
    await this.initialize();
    
    const listKey = this.generateListKey(item, mediaType);
    
    const listData = {
      id: item.id,
      title: item.title || item.name,
      poster_path: item.poster_path,
      backdrop_path: item.backdrop_path,
      overview: item.overview,
      vote_average: item.vote_average,
      release_date: item.release_date || item.first_air_date,
      mediaType,
      category,
      dateAdded: Date.now(),
      // Additional metadata for better recommendations
      genre_ids: item.genre_ids || [],
      original_language: item.original_language,
      popularity: item.popularity
    };

    this.myList.set(listKey, listData);
    await this.persistToStorage();
    return listData;
  }

  async removeFromMyList(item, mediaType) {
    await this.initialize();
    
    const listKey = this.generateListKey(item, mediaType);
    const removed = this.myList.delete(listKey);
    
    if (removed) {
      await this.persistToStorage();
    }
    
    return removed;
  }

  async updateCategory(item, mediaType, newCategory) {
    await this.initialize();
    
    const listKey = this.generateListKey(item, mediaType);
    const existingItem = this.myList.get(listKey);
    
    if (existingItem) {
      existingItem.category = newCategory;
      existingItem.lastUpdated = Date.now();
      this.myList.set(listKey, existingItem);
      await this.persistToStorage();
      return existingItem;
    }
    
    return null;
  }

  async isInMyList(item, mediaType) {
    await this.initialize();
    const listKey = this.generateListKey(item, mediaType);
    return this.myList.has(listKey);
  }

  async getItemCategory(item, mediaType) {
    await this.initialize();
    const listKey = this.generateListKey(item, mediaType);
    const listItem = this.myList.get(listKey);
    return listItem ? listItem.category : null;
  }

  async getMyListByCategory(category = null) {
    await this.initialize();
    
    let items = Array.from(this.myList.values());
    
    if (category) {
      items = items.filter(item => item.category === category);
    }
    
    // Sort by date added (most recent first)
    return items.sort((a, b) => b.dateAdded - a.dateAdded);
  }

  async getAllMyList() {
    await this.initialize();
    return Array.from(this.myList.values())
      .sort((a, b) => b.dateAdded - a.dateAdded);
  }

  async getMyListStats() {
    await this.initialize();
    
    const stats = {
      total: this.myList.size,
      byCategory: {},
      byMediaType: { movie: 0, tv: 0 },
      recentlyAdded: []
    };

    // Initialize category counts
    Object.values(MY_LIST_CATEGORIES).forEach(category => {
      stats.byCategory[category] = 0;
    });

    const items = Array.from(this.myList.values());
    
    items.forEach(item => {
      stats.byCategory[item.category]++;
      stats.byMediaType[item.mediaType]++;
    });

    // Get recently added items (last 7 days)
    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    stats.recentlyAdded = items
      .filter(item => item.dateAdded > sevenDaysAgo)
      .sort((a, b) => b.dateAdded - a.dateAdded)
      .slice(0, 10);

    return stats;
  }

  async clearMyList() {
    this.myList.clear();
    await this.persistToStorage();
  }

  async persistToStorage() {
    try {
      const dataToStore = Object.fromEntries(this.myList);
      await AsyncStorage.setItem(MY_LIST_KEY, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('Error persisting my list:', error);
    }
  }

  // Helper method to get genre preferences from My List
  async getGenrePreferences() {
    await this.initialize();
    
    const genreCount = {};
    const items = Array.from(this.myList.values());
    
    items.forEach(item => {
      if (item.genre_ids && Array.isArray(item.genre_ids)) {
        item.genre_ids.forEach(genreId => {
          genreCount[genreId] = (genreCount[genreId] || 0) + 1;
        });
      }
    });

    // Sort genres by frequency
    return Object.entries(genreCount)
      .sort(([,a], [,b]) => b - a)
      .map(([genreId, count]) => ({ genreId: parseInt(genreId), count }));
  }

  // Helper method to get language preferences
  async getLanguagePreferences() {
    await this.initialize();
    
    const languageCount = {};
    const items = Array.from(this.myList.values());
    
    items.forEach(item => {
      if (item.original_language) {
        languageCount[item.original_language] = (languageCount[item.original_language] || 0) + 1;
      }
    });

    return Object.entries(languageCount)
      .sort(([,a], [,b]) => b - a)
      .map(([language, count]) => ({ language, count }));
  }
}

export default new MyListService();
