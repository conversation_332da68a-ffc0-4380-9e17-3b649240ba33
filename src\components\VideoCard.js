import React, { memo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ImageBackground,
  Linking,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';

const VideoCard = memo(({ video }) => {
  const getVideoThumbnail = () => {
    if (video.site === 'YouTube') {
      return `https://img.youtube.com/vi/${video.key}/hqdefault.jpg`;
    }
    return null;
  };

  const getVideoUrl = () => {
    if (video.site === 'YouTube') {
      return `https://www.youtube.com/watch?v=${video.key}`;
    }
    return null;
  };

  const handlePress = async () => {
    const url = getVideoUrl();
    if (url) {
      try {
        const supported = await Linking.canOpenURL(url);
        if (supported) {
          await Linking.openURL(url);
        } else {
          Alert.alert('Error', 'Cannot open video URL');
        }
      } catch (error) {
        Alert.alert('Error', 'Failed to open video');
      }
    }
  };

  const thumbnailUrl = getVideoThumbnail();

  return (
    <View style={slothStyles.videoCardContainer}>
      <TouchableOpacity onPress={handlePress} activeOpacity={0.8}>
        <ImageBackground
          source={thumbnailUrl ? { uri: thumbnailUrl } : null}
          style={slothStyles.videoCardThumbnail}
        >
          <View style={slothStyles.videoCardPlayOverlay}>
            <Ionicons name="play" size={24} color={SLOTH_COLORS.white} style={{ marginLeft: 2 }} />
          </View>
        </ImageBackground>
      </TouchableOpacity>
      <Text style={slothStyles.videoCardTitle} numberOfLines={2}>
        {video.name}
      </Text>
    </View>
  );
});

export default VideoCard;
