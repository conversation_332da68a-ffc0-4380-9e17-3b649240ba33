import React, { memo, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import CastCard from './CastCard';

const CastRow = memo(({
  title = "Cast",
  data,
  loading = false,
  onCastPress,
}) => {
  const renderItem = useCallback(({ item }) => {
    return <CastCard castMember={item} onPress={onCastPress} />;
  }, [onCastPress]);
  
  const keyExtractor = useCallback((item) => item.id.toString(), []);

  // Card dimensions for getItemLayout optimization
  const CARD_WIDTH = 100;
  const CARD_MARGIN = 15;
  const CARD_FULL_WIDTH = CARD_WIDTH + CARD_MARGIN;

  if (loading) {
    return (
      <View style={slothStyles.castRowContainer}>
        <Text style={slothStyles.castRowTitle}>{title}</Text>
        <View style={{ height: 120, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
        </View>
      </View>
    );
  }

  if (!data || data.length === 0) {
    return null;
  }

  return (
    <View style={slothStyles.castRowContainer}>
      <Text style={slothStyles.castRowTitle}>{title}</Text>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        horizontal
        showsHorizontalScrollIndicator={false}
        nestedScrollEnabled={true}
        scrollEnabled={true}
        contentContainerStyle={slothStyles.castRowScroll}
        removeClippedSubviews={false}
        maxToRenderPerBatch={7}
        windowSize={5}
        initialNumToRender={5}
        scrollEventThrottle={16}
        getItemLayout={(_, index) => ({
          length: CARD_FULL_WIDTH,
          offset: CARD_FULL_WIDTH * index,
          index,
        })}
      />
    </View>
  );
});

export default CastRow;
